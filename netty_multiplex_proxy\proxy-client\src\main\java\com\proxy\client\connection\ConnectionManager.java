package com.proxy.client.connection;

import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.config.properties.ProxyClientProperties;
import com.proxy.client.performance.NetworkPerformanceMonitor;
import com.proxy.client.protocol.MultiplexProtocol;
import com.proxy.client.ssl.ClientSslContextManager;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import io.netty.handler.traffic.TrafficCounter;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLEngine;

import java.util.Map;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 连接管理器
 * 管理到proxy-server的长连接，支持多路复用
 */
public class ConnectionManager implements IConnectionManager {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionManager.class);

    private static final int HEARTBEAT_INTERVAL = 30; // 心跳间隔（秒）
    private static final int RECONNECT_DELAY = 5; // 重连延迟（秒）

    private static volatile ConnectionManager instance;

    private final String proxyServerHost;
    private final int proxyServerPort;
    private final EventLoopGroup eventLoopGroup;
    private final AtomicReference<Channel> connectionRef = new AtomicReference<>();
    private final ConcurrentHashMap<Integer, SessionHandler> sessions = new ConcurrentHashMap<>();
    // 等待服务器分配sessionId的请求映射 (临时sessionId -> SessionHandler)
    private final ConcurrentHashMap<Integer, SessionHandler> pendingSessions = new ConcurrentHashMap<>();
    // 简化的relay请求映射 (requestId -> SessionHandler)
    private final ConcurrentHashMap<String, SessionHandler> relayRequests = new ConcurrentHashMap<>();
    private final AtomicInteger tempSessionIdGenerator = new AtomicInteger(-1); // 使用负数作为临时ID
    private final AtomicInteger requestIdGenerator = new AtomicInteger(1); // relay请求ID生成器
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicBoolean reconnectScheduled = new AtomicBoolean(false); // 防止重复调度重连


    private volatile boolean running = false;
    private volatile boolean authenticated = false;
    private volatile boolean authenticationInProgress = false;
    private volatile boolean autoSendAuthOnConnect = true; // 控制连接时是否自动发送认证请求

    private ConnectionManager(String proxyServerHost, int proxyServerPort) {
        this.proxyServerHost = proxyServerHost;
        this.proxyServerPort = proxyServerPort;

        // Use configurable performance settings
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        ProxyClientProperties.PerformanceProperties perfConfig = configManager.getProperties().getPerformance();

        // Calculate thread count
        int workerThreads = perfConfig.getWorkerThreads();
        if (workerThreads <= 0) {
            // Automatically calculate thread count based on CPU cores
            workerThreads = Math.max(2, Runtime.getRuntime().availableProcessors() * 2);
        }

        ThreadFactory threadFactory = new DefaultThreadFactory("proxy-connection-manager");
        this.eventLoopGroup = new NioEventLoopGroup(workerThreads, threadFactory);
        logger.debug("ConnectionManager initialized with {} worker threads", workerThreads);
    }

    /**
     * 获取连接管理器实例
     */
    public static ConnectionManager getInstance(String proxyServerHost, int proxyServerPort) {
        if (instance == null) {
            synchronized (ConnectionManager.class) {
                if (instance == null) {
                    instance = new ConnectionManager(proxyServerHost, proxyServerPort);
                }
            }
        }
        return instance;
    }

    /**
     * 获取已初始化的连接管理器实例
     */
    public static ConnectionManager getInstance() {
        if (instance == null) {
            throw new IllegalStateException("ConnectionManager not initialized");
        }
        return instance;
    }

    /**
     * 创建新的连接管理器实例
     *
     * @param proxyServerHost
     * @param proxyServerPort
     * @return
     */
    public static ConnectionManager newInstance(String proxyServerHost, int proxyServerPort) {
        instance = new ConnectionManager(proxyServerHost, proxyServerPort);
        return instance;
    }

    /**
     * 设置是否在连接时自动发送认证请求
     * 当使用QueuedConnectionManager包装时，应该设置为false，让队列管理器处理认证
     */
    public void setAutoSendAuthOnConnect(boolean autoSendAuthOnConnect) {
        this.autoSendAuthOnConnect = autoSendAuthOnConnect;
    }


    /**
     * 检查连接是否已经建立
     *
     * @return true 如果连接稳定且可用于创建会话
     */
    public boolean isConnectionEstablished() {
        // 检查是否正在运行
        if (!running) {
            return false;
        }

        // 检查连接是否存在且活跃
        Channel connection = connectionRef.get();
        if (connection == null || !connection.isActive()) {
            return false;
        }

        return true;
    }

    /**
     * 检查连接是否稳定可用
     *
     * @return true 如果连接稳定且可用于创建会话
     */
    public boolean isConnectionStable() {
        // 检查是否正在运行
        if (!running) {
            return false;
        }

        // 检查连接是否存在且活跃
        Channel connection = connectionRef.get();
        if (connection == null || !connection.isActive()) {
            return false;
        }

        // 检查认证状态（如果需要认证）
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        if (configManager.isAuthEnabled()) {
            // 如果认证正在进行中，连接不稳定
            if (authenticationInProgress) {
                return false;
            }

            // 如果需要认证但未认证，连接不稳定
            if (!authenticated) {
                return false;
            }
        }

        return true;
    }

    /**
     * 启动连接管理器
     */
    public void start() {
        if (running) {
            return;
        }

        running = true;
        logger.info("启动连接管理器，目标服务器: {}:{}", proxyServerHost, proxyServerPort);

        // 建立初始连接
        connect();

        // 启动心跳任务
        scheduler.scheduleAtFixedRate(this::sendHeartbeat,
                HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);

        // 启动性能监控
        scheduler.scheduleAtFixedRate(() -> {
            NetworkPerformanceMonitor.getInstance().generatePerformanceReport();
        }, 30, 30, TimeUnit.SECONDS); // 每30秒生成一次性能报告
    }

    /**
     * 停止连接管理器
     */
    public void stop() {
        if (!running) {
            return;
        }

        running = false;
        logger.info("停止连接管理器");

        // 停止心跳任务
        scheduler.shutdown();

        // 关闭所有会话
        sessions.values().forEach(SessionHandler::close);
        sessions.clear();

        // 关闭所有等待中的会话
        pendingSessions.values().forEach(SessionHandler::close);
        pendingSessions.clear();

        // 关闭连接
        Channel connection = connectionRef.get();
        if (connection != null && connection.isActive()) {
            connection.close();
        }

        // 关闭事件循环组
        eventLoopGroup.shutdownGracefully();
    }

    /**
     * 创建新会话（使用服务器分配sessionId的新协议）
     */
    public int createSession(String targetHost, int targetPort, SessionHandler handler) {
        // 检查认证状态
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        if (configManager.isAuthEnabled()) {
            if (authenticationInProgress) {
                // 认证正在进行中，等待一段时间
                logger.debug("认证正在进行中，等待认证完成: {}:{}", targetHost, targetPort);
                scheduler.schedule(() -> {
                    createSession(targetHost, targetPort, handler);
                }, 100, java.util.concurrent.TimeUnit.MILLISECONDS);
                return -1;
            }

            if (!authenticated) {
                logger.warn("未认证，无法创建会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return -1;
            }
        }

        // 生成临时sessionId用于跟踪等待分配真实sessionId的请求
        int tempSessionId = tempSessionIdGenerator.getAndDecrement(); // 使用递减的负数
        pendingSessions.put(tempSessionId, handler);

        logger.debug("创建会话请求: tempSessionId={}, target={}:{}", tempSessionId, targetHost, targetPort);

        // 发送连接请求V2（服务器分配sessionId）
        MultiplexProtocol.Packet connectRequest =
                MultiplexProtocol.createConnectRequestV2(targetHost, targetPort);
        sendPacket(connectRequest);

        return tempSessionId; // 返回临时sessionId
    }

    /**
     * 创建TCP会话
     */
    public int createTcpSession(String targetHost, int targetPort, SessionHandler handler) {
        if (!running) {
            logger.warn("连接管理器未运行，无法创建TCP会话");
            handler.onConnectResponse(false, -1);
            return -1;
        }

        // 检查认证状态
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        if (configManager.isAuthEnabled()) {
            if (authenticationInProgress) {
                logger.warn("认证进行中，无法创建TCP会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return -1;
            }

            if (!authenticated) {
                logger.warn("未认证，无法创建TCP会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return -1;
            }
        }

        // 生成临时sessionId
        int tempSessionId = tempSessionIdGenerator.getAndDecrement();
        pendingSessions.put(tempSessionId, handler);

        logger.debug("创建TCP会话请求: tempSessionId={}, target={}:{}", tempSessionId, targetHost, targetPort);

        // 发送TCP连接请求
        MultiplexProtocol.Packet connectRequest =
                MultiplexProtocol.createTcpConnectRequest(targetHost, targetPort);
        sendPacket(connectRequest);

        return tempSessionId;
    }

    /**
     * 创建UDP会话
     */
    public int createUdpSession(String targetHost, int targetPort, SessionHandler handler) {
        if (!running) {
            logger.warn("连接管理器未运行，无法创建UDP会话");
            handler.onConnectResponse(false, -1);
            return -1;
        }

        // 检查认证状态
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        if (configManager.isAuthEnabled()) {
            if (authenticationInProgress) {
                logger.warn("认证进行中，无法创建UDP会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return -1;
            }

            if (!authenticated) {
                logger.warn("未认证，无法创建UDP会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return -1;
            }
        }

        // 生成临时sessionId
        int tempSessionId = tempSessionIdGenerator.getAndDecrement();
        pendingSessions.put(tempSessionId, handler);

        logger.debug("创建UDP会话请求: tempSessionId={}, target={}:{}", tempSessionId, targetHost, targetPort);

        // 发送UDP连接请求
        MultiplexProtocol.Packet connectRequest =
                MultiplexProtocol.createUdpConnectRequest(targetHost, targetPort);
        sendPacket(connectRequest);

        return tempSessionId;
    }

    /**
     * 创建简化的relay会话（参考traffic-3.0）
     * 不需要复杂的sessionId交互，直接生成ProxyRequest并进入relay模式
     */
    public String createRelaySession(String targetHost, int targetPort, String protocol, SessionHandler handler) {
        if (!running) {
            logger.warn("连接管理器未运行，无法创建relay会话");
            handler.onConnectResponse(false, -1);
            return null;
        }

        // 检查认证状态
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        if (configManager.isAuthEnabled()) {
            if (authenticationInProgress) {
                logger.warn("认证进行中，无法创建relay会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return null;
            }

            if (!authenticated) {
                logger.warn("未认证，无法创建relay会话: {}:{}", targetHost, targetPort);
                handler.onConnectResponse(false, -1);
                return null;
            }
        }

        // 生成唯一的requestId
        String requestId = "relay-" + requestIdGenerator.getAndIncrement() + "-" + System.currentTimeMillis();
        relayRequests.put(requestId, handler);

        logger.debug("创建relay会话请求: requestId={}, protocol={}, target={}:{}",
                requestId, protocol, targetHost, targetPort);

        // 发送简化的relay连接请求
        MultiplexProtocol.Packet relayRequest =
                MultiplexProtocol.createRelayRequest(requestId, targetHost, targetPort, protocol);
        sendPacket(relayRequest);

        return requestId; // 返回requestId用于标识
    }

    /**
     * 关闭会话
     */
    public void closeSession(int sessionId) {
        // 先检查正式会话
        SessionHandler handler = sessions.remove(sessionId);
        if (handler != null) {
            logger.debug("关闭会话: sessionId={}", sessionId);

            // 发送关闭请求
            MultiplexProtocol.Packet closePacket = MultiplexProtocol.createClosePacket(sessionId);
            sendPacket(closePacket);

            handler.close();
            return;
        }

        // 检查等待中的会话（临时sessionId）
        handler = pendingSessions.remove(sessionId);
        if (handler != null) {
            logger.debug("关闭等待中的会话: tempSessionId={}", sessionId);
            handler.close();
            // 不需要发送关闭请求，因为服务器还没有为此会话分配真正的sessionId
        }
    }

    /**
     * 发送数据
     */
    public void sendData(int sessionId, byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }

        MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createTcpDataPacket(sessionId, data);
        sendPacket(dataPacket);
    }

    /**
     * 发送数据 (ByteBuf版本) - 零拷贝优化
     */
    public void sendData(int sessionId, ByteBuf data) {
        if (data == null || data.readableBytes() == 0) {
            // Release the buffer if we're not using it
            if (data != null) {
                data.release();
            }
            return;
        }

        // 零拷贝优化：参考traffic-3.0，直接使用ByteBuf引用
        MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createZeroCopyDataPacket(sessionId, data, true);
        sendPacket(dataPacket);
    }

    /**
     * 发送UDP数据
     */
    public void sendUdpData(int sessionId, byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }

        MultiplexProtocol.Packet udpDataPacket = MultiplexProtocol.createUdpDataPacket(sessionId, data);
        sendPacket(udpDataPacket);
    }

    /**
     * 发送UDP数据 (ByteBuf版本) - 零拷贝优化
     */
    public void sendUdpData(int sessionId, ByteBuf data) {
        if (data == null || data.readableBytes() == 0) {
            if (data != null) {
                data.release();
            }
            return;
        }

        // 零拷贝优化：参考traffic-3.0，直接使用ByteBuf引用
        MultiplexProtocol.Packet udpDataPacket = MultiplexProtocol.createZeroCopyDataPacket(sessionId, data, false);
        sendPacket(udpDataPacket);
    }

    /**
     * 发送数据包（公开方法，支持队列化调用）
     */
    public void sendPacket(MultiplexProtocol.Packet packet) {
        Channel connection = connectionRef.get();
        if (connection != null && connection.isActive()) {
            ByteBuf buffer = packet.encode();
            // 性能监控：记录发送的字节数
            NetworkPerformanceMonitor.getInstance().recordBytesSent(buffer.readableBytes());

            logger.debug("发送数据包: {}", packet);
            long startTime = System.currentTimeMillis();
            connection.writeAndFlush(buffer).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.error("发送数据包失败: {}", packet, future.cause());
                    // 如果发送失败，可能是连接问题，触发重连
                    if (running) {
                        logger.info("发送失败，尝试重新连接");
                        scheduleReconnect();
                    }
                } else {
                    // 性能监控：记录发送延迟
                    long latency = System.currentTimeMillis() - startTime;
                    NetworkPerformanceMonitor.getInstance().recordLatency(latency);
                    logger.debug("数据包发送成功: {}", packet);
                }
            });
        } else {
            logger.warn("连接不可用，无法发送数据包: {}, connection={}, active={}",
                    packet, connection != null, connection != null ? connection.isActive() : false);

            // 连接不可用时，尝试重新连接
            if (running && (connection == null || !connection.isActive())) {
                logger.info("连接不可用，尝试重新连接");
                scheduleReconnect();
            }
        }
    }

    /**
     * 建立连接
     */
    private void connect() {
        if (!running) {
            return;
        }

        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(eventLoopGroup)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.SO_REUSEADDR, true)  // 性能优化：允许地址重用
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000)  // 减少连接超时，快速失败
                // 性能优化：参考traffic-3.0策略，增大缓冲区大小以提高上行吞吐量
                .option(ChannelOption.SO_RCVBUF, 128 * 1024)  // 128K接收缓冲区，提升上行性能
                .option(ChannelOption.SO_SNDBUF, 128 * 1024)  // 128K发送缓冲区，提升下行性能
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                // 性能优化：参考traffic-3.0的水位线设置，进一步优化上行传输
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(512 * 1024, 2 * 1024 * 1024))
                // 额外的性能优化选项
                .option(ChannelOption.RCVBUF_ALLOCATOR, new io.netty.channel.AdaptiveRecvByteBufAllocator(64, 1024, 65536))
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) throws Exception {
                        ChannelPipeline pipeline = ch.pipeline();

                        // 添加SSL处理器（如果启用）
                        if (ProxyClientConfigManager.getInstance().isSslEnabled()) {
                            ClientSslContextManager sslManager = ClientSslContextManager.getInstance();
                            SslContext sslContext = sslManager.getClientSslContext();
                            if (sslContext != null) {
                                SSLEngine sslEngine = sslContext.newEngine(ch.alloc(), proxyServerHost, proxyServerPort);

                                // SSL性能优化选项
                                sslEngine.setUseClientMode(true);

                                SslHandler sslHandler = new SslHandler(sslEngine);
                                sslHandler.setHandshakeTimeoutMillis(
                                        sslManager.getHandshakeTimeoutSeconds() * 1000L);

                                // 配置主机名验证
                                if (!sslManager.isVerifyHostname()) {
                                    sslHandler.engine().getSSLParameters().setEndpointIdentificationAlgorithm(null);
                                    logger.debug("已禁用SSL主机名验证");
                                } else {
                                    logger.debug("启用主机名验证");
                                }

                                pipeline.addLast("ssl", sslHandler);
                                logger.debug("为连接添加SSL处理器: {}:{}", proxyServerHost, proxyServerPort);
                            }
                        }

                        // 添加多路复用协议处理器
                        pipeline.addLast("multiplex-protocol", new MultiplexProtocolHandler());
                    }
                });

        logger.info("正在连接到代理服务器: {}:{}", proxyServerHost, proxyServerPort);

        bootstrap.connect(proxyServerHost, proxyServerPort)
                .addListener((ChannelFutureListener) future -> {
                    if (future.isSuccess()) {
                        Channel channel = future.channel();
                        if (connectionRef.compareAndSet(null, channel)) {
                            logger.info("连接到代理服务器成功: {}:{}", proxyServerHost, proxyServerPort);

                            // 重置认证状态
                            authenticated = false;
                            authenticationInProgress = false;

                            // 如果启用认证且允许自动发送，发送认证请求
                            ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
                            if (configManager.isAuthEnabled() && autoSendAuthOnConnect) {
                                authenticationInProgress = true;
                                sendAuthRequest(configManager.getAuthUsername(), configManager.getAuthPassword());
                            } else if (!configManager.isAuthEnabled()) {
                                authenticated = true; // 未启用认证时，直接标记为已认证
                            }

                            // 监听连接断开事件
                            channel.closeFuture().addListener(closeFuture -> {
                                logger.warn("与代理服务器的连接已断开: {}:{}", proxyServerHost, proxyServerPort);
                                connectionRef.set(null);
                                authenticated = false;

                                // 清理所有会话
                                cleanupSessions();

                                // 如果仍在运行，尝试重连
                                if (running) {
                                    logger.info("将在 {} 秒后尝试重连...", RECONNECT_DELAY);
                                    scheduler.schedule(this::connect, RECONNECT_DELAY, TimeUnit.SECONDS);
                                }
                            });
                        } else {
                            // 已有连接，关闭新连接
                            channel.close();
                        }
                    } else {
                        logger.error("连接到代理服务器失败: {}:{} - {}",
                                proxyServerHost, proxyServerPort, future.cause().getMessage(), future.cause());

                        // 如果仍在运行，尝试重连
                        if (running) {
                            logger.info("将在 {} 秒后尝试重连...", RECONNECT_DELAY);
                            scheduler.schedule(this::connect, RECONNECT_DELAY, TimeUnit.SECONDS);
                        }
                    }
                });
    }

    /**
     * 清理所有会话
     */
    private void cleanupSessions() {
        // 关闭所有会话
        sessions.values().forEach(SessionHandler::close);
        sessions.clear();

        // 关闭所有等待中的会话
        pendingSessions.values().forEach(SessionHandler::close);
        pendingSessions.clear();
    }

    /**
     * 安排重连
     */
    private void scheduleReconnect() {
        if (!running) {
            return;
        }

        // 使用CAS操作确保只有一个重连任务被调度
        if (reconnectScheduled.compareAndSet(false, true)) {
            logger.info("将在{}秒后重新连接", RECONNECT_DELAY);
            scheduler.schedule(() -> {
                reconnectScheduled.set(false);
                connect();
            }, RECONNECT_DELAY, TimeUnit.SECONDS);
        } else {
            logger.debug("重连已经安排，将在{}秒内执行，忽略重复的重连调度", RECONNECT_DELAY);
        }
    }

    /**
     * 发送认证请求
     */
    private void sendAuthRequest(String username, String password) {
        logger.info("发送认证请求，用户名: {}", username);
        MultiplexProtocol.Packet authRequest = MultiplexProtocol.createAuthRequestPacket(username, password);
        sendPacket(authRequest);
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (!running) {
            return;
        }

        Channel connection = connectionRef.get();
        if (connection != null && connection.isActive()) {
            MultiplexProtocol.Packet heartbeat = MultiplexProtocol.createHeartbeatPacket();
            sendPacket(heartbeat);
            logger.debug("发送心跳包");
        }
    }

    /**
     * 多路复用协议处理器
     */
    private class MultiplexProtocolHandler extends ChannelInboundHandlerAdapter {
        private ByteBuf buffer = null;

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            ByteBuf data = (ByteBuf) msg;

            NetworkPerformanceMonitor.getInstance().recordBytesReceived(data.readableBytes());
            try {
                // 累积数据
                if (buffer == null) {
                    buffer = ctx.alloc().buffer();
                }
                buffer.writeBytes(data);

                // 尝试解析数据包
                while (buffer.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                    int readerIndex = buffer.readerIndex();
                    MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(buffer);

                    if (packet == null) {
                        // 数据不完整，等待更多数据
                        buffer.readerIndex(readerIndex);
                        break;
                    }

                    // 处理数据包
                    handlePacket(packet);
                }

                // 压缩缓冲区
                if (buffer.readableBytes() == 0) {
                    buffer.clear();
                } else {
                    buffer.discardReadBytes();
                }

            } finally {
                data.release();
            }
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            logger.warn("与代理服务器的连接已断开");
            if (buffer != null) {
                buffer.release();
                buffer = null;
            }
            super.channelInactive(ctx);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.error("连接异常", cause);
            ctx.close();
        }
    }

    /**
     * 处理接收到的数据包
     */
    private void handlePacket(MultiplexProtocol.Packet packet) {
        logger.debug("收到数据包: {}", packet);

        switch (packet.getType()) {
            case MultiplexProtocol.TYPE_AUTH_RESPONSE:
                handleAuthResponse(packet);
                break;
            case MultiplexProtocol.TYPE_CONNECT_RESPONSE_V2:
                handleConnectResponseV2(packet);
                break;
            case MultiplexProtocol.TYPE_RELAY_RESPONSE:
                handleRelayResponse(packet);
                break;
            case MultiplexProtocol.TYPE_DATA:
                handleDataPacket(packet);
                break;
            case MultiplexProtocol.TYPE_CLOSE:
                handleClosePacket(packet);
                break;
            case MultiplexProtocol.TYPE_HEARTBEAT:
                // 心跳响应，无需处理
                break;
            default:
                logger.warn("未知数据包类型: {}", packet.getType());
        }
    }

    /**
     * 处理认证响应
     */
    private void handleAuthResponse(MultiplexProtocol.Packet packet) {
        try {
            byte status = MultiplexProtocol.parseAuthResponse(packet);
            boolean success = status == MultiplexProtocol.STATUS_SUCCESS;

            authenticationInProgress = false; // 清除认证进行中状态

            if (success) {
                authenticated = true;
                logger.info("认证成功");
            } else {
                authenticated = false;
                logger.error("认证失败，状态码: {}", status);

                // 认证失败，关闭连接
                Channel connection = connectionRef.get();
                if (connection != null && connection.isActive()) {
                    connection.close();
                }
            }
        } catch (Exception e) {
            logger.error("处理认证响应时发生异常", e);
            authenticationInProgress = false; // 清除认证进行中状态
            authenticated = false;

            // 异常时关闭连接
            Channel connection = connectionRef.get();
            if (connection != null && connection.isActive()) {
                connection.close();
            }
        }
    }

    /**
     * 处理连接响应V2（服务器分配sessionId）
     */
    private void handleConnectResponseV2(MultiplexProtocol.Packet packet) {
        int serverSessionId = packet.getSessionId();
        byte status = MultiplexProtocol.parseConnectResponse(packet);
        boolean success = status == MultiplexProtocol.STATUS_SUCCESS;

        logger.debug("收到连接响应V2: serverSessionId={}, status={}, success={}",
                serverSessionId, status, success);

        if (success) {
            // 从等待队列中取出第一个请求（FIFO）
            // 注意：这里假设服务器按顺序处理请求，实际应用中可能需要更复杂的匹配机制
            Integer tempSessionId = null;
            SessionHandler handler = null;

            // 找到第一个等待的会话
            for (Map.Entry<Integer, SessionHandler> entry : pendingSessions.entrySet()) {
                tempSessionId = entry.getKey();
                handler = entry.getValue();
                break;
            }

            if (tempSessionId != null && handler != null) {
                // 从等待队列移除
                pendingSessions.remove(tempSessionId);
                // 添加到正式会话映射，使用服务器分配的sessionId
                sessions.put(serverSessionId, handler);

                logger.debug("会话映射更新: tempSessionId={} -> serverSessionId={}",
                        tempSessionId, serverSessionId);

                handler.onConnectResponse(true, serverSessionId);
            } else {
                logger.warn("收到连接响应V2但没有等待的会话: serverSessionId={}", serverSessionId);
            }
        } else {
            // 连接失败，通知第一个等待的会话
            Integer tempSessionId = null;
            SessionHandler handler = null;

            for (Map.Entry<Integer, SessionHandler> entry : pendingSessions.entrySet()) {
                tempSessionId = entry.getKey();
                handler = entry.getValue();
                break;
            }

            if (tempSessionId != null && handler != null) {
                pendingSessions.remove(tempSessionId);
                handler.onConnectResponse(false, serverSessionId);
                logger.debug("连接失败通知: tempSessionId={}, serverSessionId={}, status={}",
                        tempSessionId, serverSessionId, status);
            } else {
                logger.warn("收到连接失败响应V2但没有等待的会话: serverSessionId={}", serverSessionId);
            }
        }
    }

    /**
     * 处理简化的relay连接响应
     */
    private void handleRelayResponse(MultiplexProtocol.Packet packet) {
        try {
            String[] responseData = MultiplexProtocol.parseRelayResponse(packet);
            String requestId = responseData[0];
            byte status = Byte.parseByte(responseData[1]);
            boolean success = status == MultiplexProtocol.STATUS_SUCCESS;

            logger.debug("收到relay响应: requestId={}, status={}, success={}", requestId, status, success);

            SessionHandler handler = relayRequests.remove(requestId);
            if (handler != null) {
                if (success) {
                    // 连接成功，直接进入relay模式，使用requestId作为sessionId的hash
                    int relaySessionId = requestId.hashCode();
                    sessions.put(relaySessionId, handler);
                    handler.onConnectResponse(true, relaySessionId);
                    logger.debug("Relay连接成功: requestId={}, relaySessionId={}", requestId, relaySessionId);
                } else {
                    handler.onConnectResponse(false, -1);
                    logger.debug("Relay连接失败: requestId={}, status={}", requestId, status);
                }
            } else {
                logger.warn("收到relay响应但没有对应的请求: requestId={}", requestId);
            }
        } catch (Exception e) {
            logger.error("处理relay响应时发生异常", e);
        }
    }

    /**
     * 处理数据包
     */
    private void handleDataPacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        SessionHandler handler = sessions.get(sessionId);

        if (handler != null) {
            if (packet.isUdp()) {
                // UDP数据包
                handler.onUdpData(packet.getData());
            } else {
                // TCP数据包
                handler.onData(packet.getData());
            }
        }
    }

    /**
     * 处理关闭包
     */
    private void handleClosePacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        SessionHandler handler = sessions.remove(sessionId);

        if (handler != null) {
            handler.onClose();
        }
    }

    /**
     * 获取代理服务器地址
     * @return
     */
    public String getProxyServerHost (){
        return proxyServerHost;
    }

    /**
     *  获取代理服务器端口
     * @return
     */
    public int getProxyServerPort (){
        return proxyServerPort;
    }
}
