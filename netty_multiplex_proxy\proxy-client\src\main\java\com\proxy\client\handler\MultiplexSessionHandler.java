package com.proxy.client.handler;

import com.proxy.client.connection.SessionHandler;
import com.proxy.client.connection.IConnectionManager;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.handler.codec.http.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 多路复用会话处理器
 * 处理通过代理服务器的连接会话
 */
public class MultiplexSessionHandler implements SessionHandler {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexSessionHandler.class);
    
    private final Channel clientChannel;
    private final String targetHost;
    private final int targetPort;
    private final IConnectionManager connectionManager;
    private volatile int sessionId = -1;
    private volatile String requestId; // 用于relay模式的请求ID
    private volatile boolean connected = false;
    
    public MultiplexSessionHandler(Channel clientChannel, String targetHost, int targetPort, 
                                  IConnectionManager connectionManager) {
        this.clientChannel = clientChannel;
        this.targetHost = targetHost;
        this.targetPort = targetPort;
        this.connectionManager = connectionManager;
        
        // 设置客户端通道处理器
        setupClientChannelHandler();
    }
    
    /**
     * 兼容性构造函数（已废弃，建议使用带connectionManager参数的构造函数）
     * @deprecated 使用 MultiplexSessionHandler(Channel, String, int, IConnectionManager) 替代
     */
    @Deprecated
    public MultiplexSessionHandler(Channel clientChannel, String targetHost, int targetPort) {
        this.clientChannel = clientChannel;
        this.targetHost = targetHost;
        this.targetPort = targetPort;
        this.connectionManager = null; // 将在运行时通过getInstance获取
        
        // 设置客户端通道处理器
        setupClientChannelHandler();
        
        logger.warn("使用了已废弃的构造函数，建议传入IConnectionManager参数");
    }
    
    public void setSessionId(int sessionId) {
        this.sessionId = sessionId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    @Override
    public void onConnectResponse(boolean success, int assignedSessionId) {
        if (success) {
            this.sessionId = assignedSessionId;
            this.connected = true;
            
            logger.info("代理连接建立成功: sessionId={}, target={}:{}", 
                sessionId, targetHost, targetPort);
            
            // 发送HTTP 200 Connection Established响应
            sendConnectSuccessResponse();
            
            // 移除HTTP处理器，切换到透明代理模式
            removeHttpHandlers();
            
        } else {
            logger.error("代理连接建立失败: target={}:{}", targetHost, targetPort);
            sendConnectErrorResponse();
        }
    }
    
    @Override
    public void onData(byte[] data) {
        if (clientChannel.isActive()) {
            ByteBuf buffer = clientChannel.alloc().buffer(data.length);
            buffer.writeBytes(data);
            clientChannel.writeAndFlush(buffer);
        }
    }
    
    @Override
    public void onClose() {
        logger.debug("代理会话关闭: sessionId={}, target={}:{}", 
            sessionId, targetHost, targetPort);
        
        if (clientChannel.isActive()) {
            clientChannel.close();
        }
    }
    
    @Override
    public void close() {
        logger.debug("关闭会话处理器: sessionId={}, target={}:{}", 
            sessionId, targetHost, targetPort);
        
        if (clientChannel.isActive()) {
            clientChannel.close();
        }
    }
    
    /**
     * 发送数据到代理服务器
     */
    public void sendData(byte[] data) {
        if (connected && sessionId > 0) {
            // 通过ConnectionManager发送数据
            getConnectionManager().sendData(sessionId, data);
        }
    }
    
    /**
     * 发送数据到代理服务器
     */
    public void sendData(ByteBuf data) {
        if (connected && sessionId > 0) {
            // Increase reference count since we're passing it to another thread
            data.retain();
            // 通过ConnectionManager发送数据
            getConnectionManager().sendData(sessionId, data);
        } else {
            // Release the buffer if we're not using it
            data.release();
        }
    }
    
    /**
     * 获取连接管理器（支持兼容性）
     */
    private IConnectionManager getConnectionManager() {
        if (connectionManager != null) {
            return connectionManager;
        } else {
            // 兼容性支持：使用单例模式获取
            logger.warn("使用兼容性模式获取ConnectionManager，建议使用带connectionManager参数的构造函数");
            return com.proxy.client.connection.ConnectionManager.getInstance();
        }
    }
    
    /**
     * 设置客户端通道处理器
     */
    private void setupClientChannelHandler() {
        clientChannel.pipeline().addLast("session-handler", new ChannelInboundHandlerAdapter() {
            @Override
            public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                if (connected && msg instanceof ByteBuf) {
                    // 发送数据到代理服务器 directly without copying
                    sendData((ByteBuf) msg);
                } else {
                    // 连接未建立，释放消息
                    if (msg instanceof ByteBuf) {
                        ((ByteBuf) msg).release();
                    }
                }
            }
            
            @Override
            public void channelInactive(ChannelHandlerContext ctx) throws Exception {
                logger.debug("客户端连接关闭: sessionId={}, target={}:{}", 
                    sessionId, targetHost, targetPort);
                
                // 关闭代理会话
                if (connected && sessionId > 0) {
                    getConnectionManager().closeSession(sessionId);
                }
                
                super.channelInactive(ctx);
            }
            
            @Override
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                logger.error("客户端连接异常: sessionId={}, target={}:{}, 原因: {}", 
                    sessionId, targetHost, targetPort, cause.getMessage(), cause);
                ctx.close();
            }
        });
    }
    
    /**
     * 发送连接成功响应
     */
    private void sendConnectSuccessResponse() {
        if (clientChannel.isActive()) {
            FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
            response.headers().set(HttpHeaderNames.CONNECTION, "keep-alive");
            clientChannel.writeAndFlush(response);
        }
    }
    
    /**
     * 发送连接错误响应
     */
    private void sendConnectErrorResponse() {
        if (clientChannel.isActive()) {
            FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, HttpResponseStatus.BAD_GATEWAY);
            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain");
            response.headers().set(HttpHeaderNames.CONNECTION, "close");
            
            String message = "Failed to connect to target server";
            response.content().writeBytes(message.getBytes());
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, message.length());
            
            clientChannel.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
        }
    }
    
    /**
     * 移除HTTP处理器，切换到透明代理模式
     */
    private void removeHttpHandlers() {
        try {
            ChannelPipeline pipeline = clientChannel.pipeline();
            if (pipeline.get("http-codec") != null) {
                pipeline.remove("http-codec");
            }
            if (pipeline.get("http-aggregator") != null) {
                pipeline.remove("http-aggregator");
            }
            if (pipeline.get("http-proxy-handler") != null) {
                pipeline.remove("http-proxy-handler");
            }
        } catch (Exception e) {
            logger.warn("移除HTTP处理器时发生异常: {}", e.getMessage());
        }
    }
}
