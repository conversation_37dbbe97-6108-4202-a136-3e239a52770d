package com.proxy.client.queue;

import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.connection.IConnectionManager;
import com.proxy.client.connection.SessionHandler;
import com.proxy.client.protocol.MultiplexProtocol;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 带队列的连接管理器包装类
 * 在原有ConnectionManager基础上增加队列缓冲功能
 */
public class QueuedConnectionManager implements IConnectionManager, PacketQueue.PacketSender {
    private static final Logger logger = LoggerFactory.getLogger(QueuedConnectionManager.class);
    private static final long AUTH_REQUEST_INTERVAL_MS = 10000;

    private  ConnectionManager connectionManager;
    private final PacketQueue packetQueue;

    // 队列监控相关
    private volatile boolean monitoringActive = false;
    private Thread monitoringThread;

    public QueuedConnectionManager(ConnectionManager connectionManager) {
        this.connectionManager = connectionManager;

        // 从配置文件加载队列配置
        com.proxy.client.config.ProxyClientConfigManager configManager = com.proxy.client.config.ProxyClientConfigManager
                .getInstance();

        this.packetQueue = new PacketQueue(
                configManager.getQueueCapacity(),
                configManager.getQueueBatchSize(),
                configManager.getQueueFlushIntervalMs(),
                configManager.getQueueRetryMaxAttempts(),
                configManager.getQueueRetryDelayMs());

        // 设置自己为数据包发送器
        this.packetQueue.setPacketSender(this);

        // 禁用ConnectionManager的自动认证发送，由QueuedConnectionManager通过队列处理
        this.connectionManager.setAutoSendAuthOnConnect(false);

        logger.info(
                "QueuedConnectionManager初始化完成 - 使用配置文件参数: capacity={}, batchSize={}, flushInterval={}ms, retryAttempts={}, retryDelay={}ms",
                configManager.getQueueCapacity(),
                configManager.getQueueBatchSize(),
                configManager.getQueueFlushIntervalMs(),
                configManager.getQueueRetryMaxAttempts(),
                configManager.getQueueRetryDelayMs());
    }

    public QueuedConnectionManager(ConnectionManager connectionManager,
            int queueCapacity, int batchSize, long flushIntervalMs,
            int retryAttempts, long retryDelayMs) {
        this.connectionManager = connectionManager;
        this.packetQueue = new PacketQueue(queueCapacity, batchSize, flushIntervalMs,
                retryAttempts, retryDelayMs);

        // 设置自己为数据包发送器
        this.packetQueue.setPacketSender(this);

        // 禁用ConnectionManager的自动认证发送，由QueuedConnectionManager通过队列处理
        this.connectionManager.setAutoSendAuthOnConnect(false);

        logger.info("QueuedConnectionManager初始化完成 - 自定义配置");
    }

    /**
     * 启动队列化连接管理器
     */
    @Override
    public void start() {
        logger.info("启动QueuedConnectionManager");

        // 启动队列处理
        packetQueue.start();

        // 启动底层连接管理器
        connectionManager.start();

        // 立即发送认证请求（如果需要）
        sendInitialAuthRequest();

        // 启动队列监控（如果启用）
        startQueueMonitoring();
    }

    /**
     * 停止队列化连接管理器
     */
    @Override
    public void stop() {
        logger.info("停止QueuedConnectionManager");

        // 停止队列监控
        stopQueueMonitoring();

        // 先停止队列处理
        packetQueue.stop();

        // 再停止底层连接管理器
        connectionManager.stop();
    }

    // ========== 会话管理方法（直接委托给ConnectionManager） ==========

    /**
     * 创建新会话（确保连接稳定且认证后再创建）
     */
    @Override
    public int createSession(String targetHost, int targetPort, SessionHandler handler) {
        if(connectionManager.isConnectionEstablished()){
            ensureAuthenticated();
        }
        // 检查连接是否稳定
        if (!connectionManager.isConnectionStable()) {
            logger.warn("连接不稳定，拒绝创建会话: {}:{}", targetHost, targetPort);
            handler.onConnectResponse(false, -1);
            return -1;
        }

        return connectionManager.createSession(targetHost, targetPort, handler);
    }

    /**
     * 创建TCP会话（确保连接稳定且认证后再创建）
     */
    @Override
    public int createTcpSession(String targetHost, int targetPort, SessionHandler handler) {
        if(connectionManager.isConnectionEstablished()){
           ensureAuthenticated();
        }

        // 检查连接是否稳定
        if (!connectionManager.isConnectionStable()) {
            logger.warn("连接不稳定，拒绝创建TCP会话: {}:{}", targetHost, targetPort);
            handler.onConnectResponse(false, -1);
            return -1;
        }

        return connectionManager.createTcpSession(targetHost, targetPort, handler);
    }

    /**
     * 创建UDP会话（确保连接稳定且认证后再创建）
     */
    @Override
    public int createUdpSession(String targetHost, int targetPort, SessionHandler handler) {
        if(connectionManager.isConnectionEstablished()){
            ensureAuthenticated();
        }
        // 检查连接是否稳定
        if (!connectionManager.isConnectionStable()) {
            logger.warn("连接不稳定，拒绝创建UDP会话: {}:{}", targetHost, targetPort);
            handler.onConnectResponse(false, -1);
            return -1;
        }

        return connectionManager.createUdpSession(targetHost, targetPort, handler);
    }

    /**
     * 创建简化的relay会话（确保连接稳定且认证后再创建）
     */
    @Override
    public String createRelaySession(String targetHost, int targetPort, String protocol, SessionHandler handler) {
        if(connectionManager.isConnectionEstablished()){
            ensureAuthenticated();
        }
        // 检查连接是否稳定
        if (!connectionManager.isConnectionStable()) {
            logger.warn("连接不稳定，拒绝创建relay会话: {}:{}", targetHost, targetPort);
            handler.onConnectResponse(false, -1);
            return null;
        }

        return connectionManager.createRelaySession(targetHost, targetPort, protocol, handler);
    }

    /**
     * 关闭会话（通过队列发送）
     */
    @Override
    public void closeSession(int sessionId) {
        MultiplexProtocol.Packet closePacket = MultiplexProtocol.createClosePacket(sessionId);
        enqueuePacket(closePacket, PacketQueue.PacketPriority.HIGH);
    }

    // ========== 数据发送方法（通过队列） ==========

    /**
     * 发送数据（通过队列）
     */
    @Override
    public void sendData(int sessionId, byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }

        MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createTcpDataPacket(sessionId, data);
        enqueuePacket(dataPacket, PacketQueue.PacketPriority.NORMAL);
    }

    /**
     * 发送数据（ByteBuf版本，通过队列）
     */
    @Override
    public void sendData(int sessionId, ByteBuf data) {
        if (data == null || data.readableBytes() == 0) {
            if (data != null) {
                data.release();
            }
            return;
        }

        data.retain();

        MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createTcpDataPacket(sessionId, data);
        enqueuePacket(dataPacket, PacketQueue.PacketPriority.NORMAL);
    }

    /**
     * 发送UDP数据（通过队列）
     */
    @Override
    public void sendUdpData(int sessionId, byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }

        MultiplexProtocol.Packet udpDataPacket = MultiplexProtocol.createUdpDataPacket(sessionId, data);
        enqueuePacket(udpDataPacket, PacketQueue.PacketPriority.NORMAL);
    }

    // ========== 队列相关方法 ==========

    /**
     * 将数据包加入队列
     */
    private void enqueuePacket(MultiplexProtocol.Packet packet, PacketQueue.PacketPriority priority) {
        boolean success = packetQueue.enqueue(packet, priority);
        if (!success) {
            logger.warn("数据包入队失败: {}", packet);
        }
    }

    /**
     * 实现PacketSender接口 - 实际发送数据包
     */
    @Override
    public boolean sendPacket(MultiplexProtocol.Packet packet) {
        try {
            // 直接调用ConnectionManager的公开sendPacket方法
            // 这样可以支持所有类型的数据包通过队列发送
            connectionManager.sendPacket(packet);
            return true;
        } catch (Exception e) {
            logger.error("发送数据包时发生异常: {}", packet, e);
            return false;
        }
    }

    /**
     * 获取队列统计信息
     */
    public PacketQueue.QueueStats getQueueStats() {
        return packetQueue.getStats();
    }

    // ========== 额外的队列化方法 ==========

    /**
     * 通过队列发送心跳包
     */
    public void sendHeartbeat() {
        MultiplexProtocol.Packet heartbeatPacket = MultiplexProtocol.createHeartbeatPacket();
        enqueuePacket(heartbeatPacket, PacketQueue.PacketPriority.HIGH);
    }

    /**
     * 通过队列发送认证请求
     */
    public void sendAuthRequest(String username, String password) {
        MultiplexProtocol.Packet authPacket = MultiplexProtocol.createAuthRequestPacket(username, password);
        enqueuePacket(authPacket, PacketQueue.PacketPriority.HIGH);
    }

    /**
     * 通过队列发送连接请求V2
     */
    public void sendConnectRequestV2(String targetHost, int targetPort) {
        MultiplexProtocol.Packet connectPacket = MultiplexProtocol.createConnectRequestV2(targetHost, targetPort);
        enqueuePacket(connectPacket, PacketQueue.PacketPriority.HIGH);
    }

    /**
     * 通过队列发送TCP连接请求
     */
    public void sendTcpConnectRequest(String targetHost, int targetPort) {
        MultiplexProtocol.Packet connectPacket = MultiplexProtocol.createTcpConnectRequest(targetHost, targetPort);
        enqueuePacket(connectPacket, PacketQueue.PacketPriority.HIGH);
    }

    /**
     * 通过队列发送UDP连接请求
     */
    public void sendUdpConnectRequest(String targetHost, int targetPort) {
        MultiplexProtocol.Packet connectPacket = MultiplexProtocol.createUdpConnectRequest(targetHost, targetPort);
        enqueuePacket(connectPacket, PacketQueue.PacketPriority.HIGH);
    }

    /**
     * 通过队列发送任意数据包
     */
    public void sendPacketQueued(MultiplexProtocol.Packet packet, PacketQueue.PacketPriority priority) {
        enqueuePacket(packet, priority);
    }

    // ========== 认证相关方法 ==========

    private volatile long lastAuthRequestTime = 0;

    /**
     * 发送初始认证请求
     */
    private void sendInitialAuthRequest() {
        com.proxy.client.config.ProxyClientConfigManager configManager = com.proxy.client.config.ProxyClientConfigManager
                .getInstance();

        if (configManager.isAuthEnabled()) {
            logger.info("启动时通过队列发送认证请求");

            // 延迟一点时间确保连接建立
            new Thread(() -> {
                try {
                    Thread.sleep(1000); // 等待1秒
                    sendAuthRequest(configManager.getAuthUsername(), configManager.getAuthPassword());
                    lastAuthRequestTime = System.currentTimeMillis();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, "initial-auth").start();
        }
    }

    /**
     * 确保认证状态
     * 简化版本：只在需要时发送认证请求，不进行持续监控
     */
    private void ensureAuthenticated() {
        com.proxy.client.config.ProxyClientConfigManager configManager = com.proxy.client.config.ProxyClientConfigManager
                .getInstance();

        if (!configManager.isAuthEnabled()) {
            return; // 不需要认证
        }

        long currentTime = System.currentTimeMillis();

        // 如果距离上次认证请求超过AUTH_REQUEST_INTERVAL_MS，发送新的认证请求
        if (currentTime - lastAuthRequestTime > AUTH_REQUEST_INTERVAL_MS) {
            logger.debug("发送认证请求以确保认证状态");
            sendAuthRequest(configManager.getAuthUsername(), configManager.getAuthPassword());
            lastAuthRequestTime = currentTime;

            // 等待一小段时间让认证请求处理
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // ========== 队列监控相关方法 ==========

    /**
     * 启动队列监控
     */
    private void startQueueMonitoring() {
        com.proxy.client.config.ProxyClientConfigManager configManager = com.proxy.client.config.ProxyClientConfigManager
                .getInstance();

        if (!configManager.isQueueMonitoringEnabled()) {
            logger.debug("队列监控已禁用");
            return;
        }

        if (monitoringActive) {
            return;
        }

        monitoringActive = true;
        monitoringThread = new Thread(this::monitorQueue, "queue-monitor");
        monitoringThread.setDaemon(true);
        monitoringThread.start();

        logger.info("队列监控已启动 - 报告间隔: {}秒, 警告阈值: {}%, 错误阈值: {}%",
                configManager.getQueueMonitoringReportIntervalSeconds(),
                configManager.getQueueMonitoringWarningThreshold(),
                configManager.getQueueMonitoringErrorThreshold());
    }

    /**
     * 停止队列监控
     */
    private void stopQueueMonitoring() {
        monitoringActive = false;
        if (monitoringThread != null) {
            monitoringThread.interrupt();
        }
        logger.info("队列监控已停止");
    }

    /**
     * 监控队列状态
     */
    private void monitorQueue() {
        com.proxy.client.config.ProxyClientConfigManager configManager = com.proxy.client.config.ProxyClientConfigManager
                .getInstance();

        int reportIntervalSeconds = configManager.getQueueMonitoringReportIntervalSeconds();
        int warningThreshold = configManager.getQueueMonitoringWarningThreshold();
        int errorThreshold = configManager.getQueueMonitoringErrorThreshold();
        int queueCapacity = configManager.getQueueCapacity();

        while (monitoringActive && !Thread.currentThread().isInterrupted()) {
            try {
                PacketQueue.QueueStats stats = packetQueue.getStats();

                // 计算队列使用率
                int usagePercent = (int) ((stats.getQueueSize() * 100.0) / queueCapacity);

                // 根据使用率记录不同级别的日志
                if (usagePercent >= errorThreshold) {
                    logger.error("队列使用率过高: {}% ({}/{}), 入队: {}, 处理: {}, 丢弃: {}",
                            usagePercent, stats.getQueueSize(), queueCapacity,
                            stats.getEnqueuedCount(), stats.getProcessedCount(), stats.getDroppedCount());
                } else if (usagePercent >= warningThreshold) {
                    logger.warn("队列使用率较高: {}% ({}/{}), 入队: {}, 处理: {}, 丢弃: {}",
                            usagePercent, stats.getQueueSize(), queueCapacity,
                            stats.getEnqueuedCount(), stats.getProcessedCount(), stats.getDroppedCount());
                } else {
                    logger.info("队列状态正常: {}% ({}/{}), 入队: {}, 处理: {}, 丢弃: {}",
                            usagePercent, stats.getQueueSize(), queueCapacity,
                            stats.getEnqueuedCount(), stats.getProcessedCount(), stats.getDroppedCount());
                }

                // 等待下次报告
                Thread.sleep(reportIntervalSeconds * 1000L);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("队列监控异常", e);
                try {
                    Thread.sleep(10000); // 异常时等待10秒再继续
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    /**
     * 获取底层ConnectionManager实例
     */
    public ConnectionManager getConnectionManager() {
        return connectionManager;
    }

    public void setConnectionManager(ConnectionManager connectionManager) {
        this.connectionManager = connectionManager;
    }
}