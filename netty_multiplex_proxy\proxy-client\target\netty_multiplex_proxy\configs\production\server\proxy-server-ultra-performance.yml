# 超高性能代理服务器配置文件 - 本机部署优化版
# 专门针对本机部署上行速度优化，参考traffic-3.0优化策略
# 适用于极高并发、超大流量的生产环境
# 建议配合高性能硬件使用 (16+ CPU cores, 32GB+ RAM)

# 服务器配置
server:
  port: 8888

# 认证配置 - 生产环境优化
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 60  # 增加超时时间，减少频繁重认证

# 连接池配置 - 超高性能优化（参考traffic-3.0连接复用策略）
pool:
  enable: true
  max-connections:
    per-host: 200  # 大幅增加每主机最大连接数，提升上行并发能力
  idle-timeout:
    seconds: 600   # 10分钟空闲超时，最大化连接复用
  cleanup-interval:
    seconds: 180   # 3分钟清理间隔，减少清理频率
  max-lifetime:
    seconds: 14400  # 连接最大存活时间4小时，减少连接重建开销

# 性能监控配置 - 优化监控开销
metrics:
  enable: true
  report:
    interval:
      seconds: 600  # 10分钟报告一次，进一步降低监控开销

# 黑名单配置 - 智能优化
blacklist:
  enable: true
  failure:
    threshold: 10  # 进一步增加失败阈值，减少误判
  cache:
    timeout:
      seconds: 900  # 15分钟黑名单缓存，减少重复检查

# 超高性能线程池配置（参考traffic-3.0的DispatchProcessor优化）
performance:
  # Boss线程数 (本机部署可以使用更多Boss线程)
  boss-threads: 6
  # 工作线程数 (0表示自动计算，推荐让系统自动计算)
  worker-threads: 0
  # I/O操作与CPU操作的比例，代理服务器I/O密集，设置最高值
  io-ratio: 95
  # 启用智能线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (0表示使用默认计算值)
  max-worker-threads: 0
  # 最小工作线程数 (确保基础并发能力)
  min-worker-threads: 24

# 网络优化配置 - 参考traffic-3.0的网络优化策略
network:
  # 缓冲区配置 - 针对上行速度优化
  buffer:
    # 接收缓冲区大小 (2MB，提升上行数据接收能力)
    receive-buffer-size: 2097152
    # 发送缓冲区大小 (2MB，提升下行数据发送能力)
    send-buffer-size: 2097152
    # 写入缓冲区水位标记 - 低水位512KB，高水位2MB
    write-buffer-low-water-mark: 524288
    write-buffer-high-water-mark: 2097152

  # TCP优化配置
  tcp:
    # 禁用Nagle算法，减少延迟
    no-delay: true
    # 启用TCP保活机制
    keep-alive: true
    # 启用地址重用
    reuse-address: true
    # 连接超时时间 (3秒，快速失败)
    connect-timeout: 3000
    # SO_LINGER设置为0，快速关闭连接
    so-linger: 0

  # 连接队列配置
  connection:
    # 增大backlog队列，提升并发连接处理能力
    backlog: 8192
    # 最大连接数
    max-connections: 100000

# 批处理优化配置 - 参考traffic-3.0的批量处理策略
batch:
  # 启用批处理优化
  enable: true
  # 批处理大小 (参考ProxyProcessorConfig的优化)
  size: 32
  # 批处理超时时间 (5ms，快速响应)
  timeout-ms: 5
  # 启用自适应调整
  adaptive: true

# SSL配置 (如果需要)
ssl:
  enable: false
  keystore:
    path: ""
    password: ""
    type: "JKS"
  truststore:
    path: ""
    password: ""
    type: "JKS"

# 地理位置过滤配置 - 关闭以减少处理开销
geo-filter:
  enable: false
  china-only: false
  block-overseas-suspicious: false

# 恶意内容过滤配置 - 关闭以减少处理开销
malicious-filter:
  enable: false
  domain-filter:
    enable: false
  keyword-filter:
    enable: false

# 内存优化配置 - 参考traffic-3.0的内存管理策略
memory:
  # 启用内存优化
  optimization: true
  # 内存使用阈值 (80%时触发优化)
  threshold: 0.8
  # 缓冲区优化
  buffer-optimization: true
  # 启用零拷贝优化
  zero-copy: true

# 超高性能JVM参数建议 (在启动脚本中使用) - 针对本机部署优化:
# 内存配置:
# -Xms8g -Xmx16g                      # 设置大内存堆
# -XX:NewRatio=1                       # 年轻代与老年代比例1:1
# -XX:MaxDirectMemorySize=8g           # 设置直接内存大小

# 垃圾收集器配置:
# -XX:+UseG1GC                         # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=100             # 限制GC暂停时间100ms
# -XX:G1HeapRegionSize=16m             # 设置G1区域大小
# -XX:+G1UseAdaptiveIHOP               # 启用自适应IHOP
# -XX:G1MixedGCCountTarget=8           # 混合GC目标次数

# 编译优化:
# -XX:+UseStringDeduplication          # 启用字符串去重
# -XX:+OptimizeStringConcat            # 优化字符串连接
# -XX:+UseFastAccessorMethods          # 使用快速访问器方法
# -XX:+AggressiveOpts                  # 启用激进优化

# Netty优化:
# -Dio.netty.allocator.type=pooled     # 使用池化内存分配器
# -Dio.netty.allocator.numDirectArenas=16  # 设置直接内存区域数
# -Dio.netty.allocator.numHeapArenas=16    # 设置堆内存区域数
# -Dio.netty.allocator.pageSize=8192   # 设置页面大小
# -Dio.netty.allocator.maxOrder=11     # 设置最大阶数
# -Dio.netty.recycler.maxCapacityPerThread=0  # 禁用对象回收器
# -Dio.netty.leakDetection.level=DISABLED     # 禁用内存泄漏检测(生产环境)

# 系统优化:
# -Djava.net.preferIPv4Stack=true      # 优先使用IPv4
# -Dsun.net.useExclusiveBind=false     # 允许端口复用
# -Djava.security.egd=file:/dev/./urandom  # 使用更快的随机数生成器

# 监控和调试 (可选):
# -XX:+PrintGC                         # 打印GC信息
# -XX:+PrintGCDetails                  # 打印详细GC信息
# -XX:+PrintGCTimeStamps               # 打印GC时间戳
# -Xloggc:gc.log                       # GC日志文件
# -XX:+UseGCLogFileRotation            # 启用GC日志轮转
# -XX:NumberOfGCLogFiles=5             # GC日志文件数量
# -XX:GCLogFileSize=100M               # GC日志文件大小

# 操作系统级别优化建议:
# 1. 增加文件描述符限制: ulimit -n 1048576
# 2. 优化TCP参数:
#    net.core.somaxconn = 65535
#    net.core.netdev_max_backlog = 5000
#    net.ipv4.tcp_max_syn_backlog = 65535
#    net.ipv4.tcp_fin_timeout = 30
#    net.ipv4.tcp_keepalive_time = 1200
#    net.ipv4.tcp_rmem = 4096 65536 16777216
#    net.ipv4.tcp_wmem = 4096 65536 16777216
# 3. 禁用swap: swapoff -a
# 4. 使用高性能网络驱动和网卡
# 5. 绑定进程到特定CPU核心: taskset

# 硬件建议:
# - CPU: 16+ 核心，高主频 (3.0GHz+)
# - 内存: 32GB+ DDR4
# - 网络: 万兆网卡
# - 存储: NVMe SSD
# - 操作系统: Linux (推荐 Ubuntu 20.04+ 或 CentOS 8+)
