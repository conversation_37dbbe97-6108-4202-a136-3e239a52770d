package com.xiang.proxy.server.core;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.router.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 批量处理优化的代理处理器
 * 支持批量从队列中取出请求进行处理，提高吞吐量
 */
public class BatchProxyProcessor extends ProxyProcessor {
    private static final Logger logger = LoggerFactory.getLogger(BatchProxyProcessor.class);
    
    private final int baseBatchSize;
    private final long baseBatchTimeoutMs;

    public BatchProxyProcessor(Router router) {
        super(router);
        ProxyProcessorConfig config = getConfig();
        this.baseBatchSize = config.getBatchSize();
        this.baseBatchTimeoutMs = config.getBatchTimeoutMs();
    }

    public BatchProxyProcessor(Router router, ProxyProcessorConfig config) {
        super(router, config);
        this.baseBatchSize = config.getBatchSize();
        this.baseBatchTimeoutMs = config.getBatchTimeoutMs();
    }

    /**
     * 获取当前有效的批次大小（考虑自适应调整）
     */
    private int getCurrentBatchSize() {
        AdaptiveQueueManager adaptiveManager = getAdaptiveManager();
        if (adaptiveManager != null) {
            return adaptiveManager.getCurrentBatchSize();
        }
        return baseBatchSize;
    }

    /**
     * 获取当前有效的轮询超时（考虑自适应调整）
     */
    private long getCurrentPollTimeout() {
        AdaptiveQueueManager adaptiveManager = getAdaptiveManager();
        if (adaptiveManager != null) {
            return adaptiveManager.getCurrentPollTimeout();
        }
        return baseBatchTimeoutMs;
    }

    /**
     * 批量处理队列 - 重写父类方法
     */
    @Override
    protected void processQueue(int queueIndex) {
        logger.debug("批量队列 {} 工作线程启动，基础批次大小: {}, 基础超时: {}ms", 
                queueIndex, baseBatchSize, baseBatchTimeoutMs);
        BlockingQueue<QueuedRequest> queue = getRequestQueue(queueIndex);
        
        while (getRunningState().get()) {
            try {
                // 获取当前自适应参数
                int currentBatchSize = getCurrentBatchSize();
                long currentTimeout = getCurrentPollTimeout();
                
                List<QueuedRequest> batch = collectBatch(queue, currentBatchSize, currentTimeout);
                
                if (!batch.isEmpty()) {
                    processBatch(batch, queueIndex);
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("批量队列 {} 工作线程被中断", queueIndex);
                break;
            } catch (Exception e) {
                logger.error("批量队列 {} 处理请求时发生异常", queueIndex, e);
            }
        }
        
        logger.debug("批量队列 {} 工作线程结束", queueIndex);
    }

    /**
     * 收集一个批次的请求
     */
    private List<QueuedRequest> collectBatch(BlockingQueue<QueuedRequest> queue, int batchSize, long batchTimeoutMs) throws InterruptedException {
        List<QueuedRequest> batch = new ArrayList<>(batchSize);
        
        // 阻塞等待第一个请求
        QueuedRequest firstRequest = queue.poll(1, TimeUnit.MILLISECONDS);
        if (firstRequest != null) {
            batch.add(firstRequest);
            
            // 尝试获取更多请求组成批次
            long startTime = System.currentTimeMillis();
            while (batch.size() < batchSize && 
                   (System.currentTimeMillis() - startTime) < batchTimeoutMs) {
                
                QueuedRequest request = queue.poll(1, TimeUnit.MILLISECONDS);
                if (request != null) {
                    batch.add(request);
                } else {
                    break; // 没有更多请求，结束批次收集
                }
            }
        }
        
        return batch;
    }

    /**
     * 处理一个批次的请求 - 优化版本，避免过度并行化
     */
    private void processBatch(List<QueuedRequest> batch, int queueIndex) {
        logger.debug("队列 {} 开始处理批次，大小: {}", queueIndex, batch.size());

        long startTime = System.currentTimeMillis();

        // 优化：对于小批次使用顺序处理，避免并行开销
        if (batch.size() <= 5) {
            // 小批次顺序处理，减少线程切换开销
            for (QueuedRequest queuedRequest : batch) {
                try {
                    processQueuedRequest(queuedRequest, queueIndex);
                } catch (Exception e) {
                    logger.error("批次处理中的请求失败: {}",
                            queuedRequest.getRequest().getRequestId(), e);
                    queuedRequest.getFuture().complete(
                            ProxyResponse.error(queuedRequest.getRequest().getRequestId(),
                                    e.getMessage()));
                }
            }
        } else {
            // 大批次并行处理
            batch.parallelStream().forEach(queuedRequest -> {
                try {
                    processQueuedRequest(queuedRequest, queueIndex);
                } catch (Exception e) {
                    logger.error("批次处理中的请求失败: {}",
                            queuedRequest.getRequest().getRequestId(), e);
                    queuedRequest.getFuture().complete(
                            ProxyResponse.error(queuedRequest.getRequest().getRequestId(),
                                    e.getMessage()));
                }
            });
        }

        long processingTime = System.currentTimeMillis() - startTime;
        logger.debug("队列 {} 批次处理完成，大小: {}, 耗时: {}ms",
                queueIndex, batch.size(), processingTime);
    }

    /**
     * 获取批处理统计信息
     */
    public BatchStats getBatchStats() {
        QueueStats queueStats = getQueueStats();
        return new BatchStats(
                getCurrentBatchSize(),
                getCurrentPollTimeout(),
                queueStats.getTotalQueueSize(),
                queueStats.getProcessedRequests()
        );
    }

    /**
     * 批处理统计信息
     */
    public static class BatchStats {
        private final int batchSize;
        private final long batchTimeoutMs;
        private final int totalQueueSize;
        private final int processedRequests;

        public BatchStats(int batchSize, long batchTimeoutMs, int totalQueueSize, int processedRequests) {
            this.batchSize = batchSize;
            this.batchTimeoutMs = batchTimeoutMs;
            this.totalQueueSize = totalQueueSize;
            this.processedRequests = processedRequests;
        }

        public int getBatchSize() { return batchSize; }
        public long getBatchTimeoutMs() { return batchTimeoutMs; }
        public int getTotalQueueSize() { return totalQueueSize; }
        public int getProcessedRequests() { return processedRequests; }

        @Override
        public String toString() {
            return String.format("BatchStats{batchSize=%d, batchTimeoutMs=%d, " +
                    "totalQueueSize=%d, processedRequests=%d}",
                    batchSize, batchTimeoutMs, totalQueueSize, processedRequests);
        }
    }
}