package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.auth.AuthManager;
import com.xiang.proxy.server.blacklist.HostBlacklist;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.filter.FilterResult;
import com.xiang.proxy.server.filter.GeoLocationFilter;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.outbound.AsyncOutboundConnection;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.util.ConnectionKeyUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 多路复用会话 V2
 * 集成了完整的认证、连接池、黑名单、地理位置过滤、性能监控功能
 */
public class MultiplexSession {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexSession.class);

    // 每个客户端的最大会话数限制
    private static final int MAX_SESSIONS_PER_CLIENT = 200;
    // 已关闭会话的短期缓存TTL，用于抑制重复日志（毫秒）
    private static final long CLOSED_SESSION_TTL_MS = 10_000L;

    public static final String REQUEST_ATTR_KEY = "multiplex.session";

    private final Channel clientChannel;
    private final ProxyProcessor proxyProcessor;
    private final long clientConnectionId;
    private final Map<Integer, String> sessionConnections = new ConcurrentHashMap<>();
    private final Map<Integer, String> sessionHostKeys = new ConcurrentHashMap<>(); // 会话ID到主机键值的映射
    // 已关闭/出错会话的时间戳，避免客户端重复发送数据导致日志泛滥
    private final ConcurrentHashMap<Integer, Long> terminatedSessions = new ConcurrentHashMap<>();
    // 正在建立连接的会话ID集合，用于避免竞态条件
    private final ConcurrentHashMap<Integer, Long> pendingSessions = new ConcurrentHashMap<>();
    private final AtomicInteger sessionIdGenerator = new AtomicInteger(1); // 从1开始生成sessionId
    private final Queue<Integer> reusableSessionIds = new ConcurrentLinkedQueue<>(); // 可重用的会话ID队列
    private ByteBuf buffer;

    // 背压控制：限制并发处理的数据包数量
    private final AtomicInteger pendingDataPackets = new AtomicInteger(0);
    private static final int MAX_PENDING_DATA_PACKETS = 100;

    // 添加性能指标收集实例
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();

    public MultiplexSession(Channel clientChannel, ProxyProcessor proxyProcessor, long clientConnectionId) {
        this.clientChannel = clientChannel;
        this.proxyProcessor = proxyProcessor;
        this.clientConnectionId = clientConnectionId;
    }

    /**
     * 处理接收到的数据 - 优化版本，减少不必要的数据拷贝
     */
    public void processData(ByteBuf data) {
        try {
            // 优化：延迟创建缓冲区，减少内存分配
            if (buffer == null) {
                // 如果数据足够大，直接处理而不创建缓冲区
                if (data.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                    if (tryProcessDirectly(data)) {
                        return; // 直接处理成功，无需缓冲
                    }
                }
                // 需要缓冲，创建适当大小的缓冲区，但避免过度分配
                int initialCapacity = Math.min(data.readableBytes() * 2, 8192); // 限制最大初始容量
                buffer = clientChannel.alloc().buffer(Math.max(initialCapacity, 1024));
            }

            buffer.writeBytes(data);

            // 尝试解析数据包
            while (buffer.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                int readerIndex = buffer.readerIndex();
                MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(buffer);

                if (packet == null) {
                    // 数据不完整，等待更多数据
                    buffer.readerIndex(readerIndex);
                    break;
                }

                // 处理数据包
                handlePacket(packet);
            }

            // 优化缓冲区管理
            optimizeBuffer();

        } finally {
            data.release();
        }
    }

    /**
     * 尝试直接处理数据而不使用缓冲区
     */
    private boolean tryProcessDirectly(ByteBuf data) {
        if (data.readableBytes() < MultiplexProtocol.HEADER_LENGTH) {
            return false;
        }

        int readerIndex = data.readerIndex();
        MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(data);

        if (packet == null) {
            // 数据不完整，恢复读取位置
            data.readerIndex(readerIndex);
            return false;
        }

        // 处理数据包
        handlePacket(packet);

        // 如果还有剩余数据，返回false以便使用缓冲区处理
        return data.readableBytes() == 0;
    }

    /**
     * 处理数据包
     */
    private void handlePacket(MultiplexProtocol.Packet packet) {
        logger.debug("收到多路复用数据包: {}", packet);

        try {
            switch (packet.getType()) {
                case MultiplexProtocol.TYPE_AUTH_REQUEST:
                    handleAuthRequest(packet);
                    break;
                case MultiplexProtocol.TYPE_RELAY_REQUEST:
                    handleRelayRequest(packet);
                    break;
                case MultiplexProtocol.TYPE_DATA:
                    handleDataPacket(packet);
                    break;
                case MultiplexProtocol.TYPE_CLOSE:
                    handleClosePacket(packet);
                    break;
                case MultiplexProtocol.TYPE_HEARTBEAT:
                    handleHeartbeat(packet);
                    break;
                default:
                    logger.warn("未知数据包类型: {}", packet.getType());
            }
        } catch (Exception e) {
            logger.error("处理数据包时发生异常: {}", packet, e);
        }
    }

    /**
     * 处理认证请求
     */
    private void handleAuthRequest(MultiplexProtocol.Packet packet) {
        try {
            // 解析认证数据
            String[] credentials = MultiplexProtocol.parseAuthRequest(packet);
            String username = credentials[0];
            String password = credentials[1];

            logger.debug("连接 {} 收到认证请求，用户: {}", clientConnectionId, username);

            // 执行认证
            boolean success = AuthManager.getInstance().authenticate(clientChannel, username, password);

            // 发送认证响应
            byte status = success ? MultiplexProtocol.STATUS_SUCCESS : MultiplexProtocol.STATUS_AUTH_FAILED;
            MultiplexProtocol.Packet response = MultiplexProtocol.createAuthResponsePacket(status);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer);

            if (success) {
                logger.info("连接 {} 认证成功，用户: {}", clientConnectionId, username);
            } else {
                logger.warn("连接 {} 认证失败，用户: {}，即将关闭连接", clientConnectionId, username);
                // 认证失败后延迟关闭连接，给客户端时间接收响应
                clientChannel.eventLoop().schedule(() -> {
                    clientChannel.close();
                }, 1, java.util.concurrent.TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            logger.error("处理认证请求时发生异常 (连接ID: {}): {}", clientConnectionId, e.getMessage(), e);

            // 发送认证失败响应
            MultiplexProtocol.Packet response = MultiplexProtocol
                    .createAuthResponsePacket(MultiplexProtocol.STATUS_AUTH_FAILED);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer);

            // 关闭连接
            clientChannel.close();
        }
    }


    /**
     * 处理简化的relay连接请求（参考traffic-3.0）
     */
    private void handleRelayRequest(MultiplexProtocol.Packet packet) {
        try {
            // 检查认证状态
            if (AuthManager.getInstance().requiresAuth(clientChannel)) {
                logger.warn("连接 {} 未认证，拒绝relay连接请求", clientConnectionId);
                sendRelayResponse("unknown", MultiplexProtocol.STATUS_AUTH_REQUIRED);
                return;
            }

            // 检查认证超时
            if (AuthManager.getInstance().isAuthTimeout(clientChannel)) {
                logger.warn("连接 {} 认证超时，关闭连接", clientConnectionId);
                clientChannel.close();
                return;
            }

            String[] relayData = MultiplexProtocol.parseRelayRequest(packet);
            String requestId = relayData[0];
            String protocol = relayData[1];
            String host = relayData[2];
            int port = Integer.parseInt(relayData[3]);

            logger.info("客户端连接 {} 请求relay连接: requestId={}, protocol={}, target={}:{}",
                    clientConnectionId, requestId, protocol, host, port);

            // 检查主机是否在黑名单中
            if (HostBlacklist.getInstance().isBlacklisted(host)) {
                PerformanceMetrics.getInstance().incrementBlacklistHits();
                logger.warn("主机 {} 在黑名单中，拒绝relay连接请求 (连接ID: {}, requestId: {})",
                        host, clientConnectionId, requestId);
                sendRelayResponse(requestId, MultiplexProtocol.STATUS_FORBIDDEN);
                return;
            }

            // 检查地理位置过滤
            FilterResult filterResult = GeoLocationFilter.getInstance().checkAccess(host, port);
            if (!filterResult.isAllowed()) {
                logger.warn("主机 {} 被地理位置过滤器拒绝: {} (连接ID: {}, requestId: {})",
                        host, filterResult.getReason(), clientConnectionId, requestId);
                sendRelayResponse(requestId, MultiplexProtocol.STATUS_FORBIDDEN);
                return;
            }

            // 创建代理请求，使用requestId的hash作为sessionId
            int relaySessionId = requestId.hashCode();
            String requestProtocol = "UDP".equalsIgnoreCase(protocol) ?
                    ProxyRequest.Protocol.UDP : ProxyRequest.Protocol.TCP;

            ProxyRequest request = ProxyRequest.builder()
                    .protocol(requestProtocol)
                    .target(host, port)
                    .clientChannel(clientChannel)
                    .sessionId(relaySessionId)
                    .clientId(clientChannel.id().asShortText())
                    .requestId(requestId)
                    .attribute(ProxyRequest.Attributes.ORIGINAL_PROTOCOL, ProxyRequest.Protocol.MULTIPLEX)
                    .attribute(REQUEST_ATTR_KEY, this)
                    .build();

            // 通过ProxyProcessor处理请求，直接进入relay模式
            long startTime = System.currentTimeMillis();
            proxyProcessor.processRequest(request)
                    .whenComplete((response, throwable) -> {
                        long elapsedTime = System.currentTimeMillis() - startTime;

                        if (throwable != null) {
                            logger.error("处理relay连接请求失败: requestId={}, target={}:{}, 耗时: {}ms",
                                    requestId, host, port, elapsedTime, throwable);

                            advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                            advancedMetrics.recordError("backend_connection_failed");
                            HostBlacklist.getInstance().recordFailure(host);

                            sendRelayResponse(requestId, MultiplexProtocol.STATUS_FAILED);
                        } else if (response.isSuccess() && response.hasConnection()) {
                            logger.debug("Relay连接请求处理成功: requestId={}, target={}:{}, 耗时: {}ms",
                                    requestId, host, port, elapsedTime);

                            advancedMetrics.recordConnectionQuality(host, true, elapsedTime);
                            HostBlacklist.getInstance().recordSuccess(host);

                            String connectionId = response.getConnection().getConnectionId();
                            String hostKey = ConnectionKeyUtils.generateQueueKey(request);

                            // 原子性地建立会话映射，确保并发安全
                            boolean sessionEstablished = establishSessionMapping(relaySessionId, connectionId, hostKey);
                            if (sessionEstablished) {
                                // 注册会话路由信息到后端数据路由器
                                MultiplexBackendDataRouter.registerSessionRoute(
                                    connectionId, relaySessionId, clientChannel, this::handleSessionError);

                                sendRelayResponse(requestId, MultiplexProtocol.STATUS_SUCCESS);
                            } else {
                                logger.warn("会话映射建立失败，可能存在并发冲突: sessionId={}, connectionId={}",
                                           relaySessionId, connectionId);
                                sendRelayResponse(requestId, MultiplexProtocol.STATUS_FAILED);
                                // 关闭连接，避免资源泄漏
                                proxyProcessor.closeConnection(connectionId);
                                return;
                            }

                            PerformanceMetrics.getInstance().incrementTotalSessions();
                            PerformanceMetrics.getInstance().incrementActiveSessions();
                        } else {
                            logger.warn("Relay连接请求处理失败: requestId={}, target={}:{}, reason={}, 耗时: {}ms",
                                    requestId, host, port, response.getMessage(), elapsedTime);

                            advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                            HostBlacklist.getInstance().recordFailure(host);

                            sendRelayResponse(requestId, MultiplexProtocol.STATUS_FAILED);
                        }
                    });

        } catch (Exception e) {
            logger.error("解析relay连接请求失败: {}", packet, e);
            sendRelayResponse("unknown", MultiplexProtocol.STATUS_FAILED);
        }
    }

    /**
     * 处理数据包
     */
    private void handleDataPacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();

        // 原子性地获取会话连接信息，确保并发安全
        SessionConnectionInfo sessionInfo = getSessionConnectionInfo(sessionId);

        if (sessionInfo == null) {
            // 检查会话是否正在建立连接中
            if (isPendingSession(sessionId)) {
                logger.debug("会话正在建立连接中，暂时缓存数据: sessionId={}", sessionId);
                // TODO: 实现数据缓存机制，或者返回背压信号
                sendCloseNotification(sessionId, "连接建立中，请稍后重试");
                return;
            }

            // 若该会话近期已关闭，静默丢弃以抑制日志泛滥
            Long closedAt = terminatedSessions.get(sessionId);
            long now = System.currentTimeMillis();
            if (closedAt != null && (now - closedAt) < CLOSED_SESSION_TTL_MS) {
                logger.debug("收到已关闭会话的数据，忽略: sessionId={}", sessionId);
                return;
            }

            // 首次或超过TTL后再次收到，告警一次并发送关闭通知
            logger.warn("未找到会话对应的连接: sessionId={}", sessionId);
            terminatedSessions.put(sessionId, now);
            sendCloseNotification(sessionId, "会话不存在");
            return;
        }

        String connectionId = sessionInfo.connectionId;

        // 获取连接并检查状态
        OutboundConnection connection = proxyProcessor.getActiveConnection(connectionId);
        if (connection == null) {
            logger.debug("连接不存在: sessionId={}, connectionId={}", sessionId, connectionId);
            handleSessionError(sessionId, "连接不存在");
            return;
        }

        if (!connection.isActive()) {
            logger.debug("连接已关闭: sessionId={}, connectionId={}", sessionId, connectionId);
            handleSessionError(sessionId, "连接已关闭");
            return;
        }

        // 获取对应的outbound处理器
        String outboundId = connection.getAttribute(OutboundConnection.Attributes.OUTBOUND_ID);
        OutboundHandler outboundHandler = proxyProcessor.getOutboundHandler(outboundId);

        if (outboundHandler == null) {
            logger.error("未找到outbound处理器: outboundId={}", outboundId);
            handleSessionError(sessionId, "outbound处理器不存在");
            return;
        }

        // 背压控制：检查并发数据包处理数量
        int currentPending = pendingDataPackets.get();
        if (currentPending >= MAX_PENDING_DATA_PACKETS) {
            logger.warn("并发数据包处理数量过多 ({}), 暂时丢弃数据包: sessionId={} (连接ID: {})",
                    currentPending, sessionId, clientConnectionId);
            // 发送背压信号给客户端
            sendCloseNotification(sessionId, "服务器繁忙，请稍后重试");
            return;
        }

        // 获取数据并通过outbound处理器发送
        ByteBuf dataBuf = packet.getDataBuf();
        if (dataBuf != null && dataBuf.isReadable()) {
            int dataSize = dataBuf.readableBytes();

            // 增加并发计数
            pendingDataPackets.incrementAndGet();

            // 直接通过outbound处理器发送数据，不检查后端连接状态
            // 让outbound处理器负责连接管理和数据发送
            outboundHandler.sendData(connection, dataBuf)
                    .whenComplete((result, throwable) -> {
                        // 减少并发计数
                        pendingDataPackets.decrementAndGet();

                        if (throwable != null) {
                            // 使用智能错误分类，避免Connection reset by peer的ERROR级别日志
                            if (isConnectionResetError(throwable)) {
                                logger.debug("会话连接被重置: sessionId={}, connectionId={} (连接ID: {})",
                                        sessionId, connectionId, clientConnectionId);
                            } else if (isClosedChannelError(throwable)) {
                                logger.debug("会话通道已关闭: sessionId={}, connectionId={} (连接ID: {})",
                                        sessionId, connectionId, clientConnectionId);
                            } else {
                                logger.warn("发送数据到outbound失败: sessionId={}, connectionId={} (连接ID: {}), error={}",
                                        sessionId, connectionId, clientConnectionId, throwable.getMessage());
                            }
                            // 发送失败，关闭会话
                            handleSessionError(sessionId, "数据发送失败: " + throwable.getMessage());
                        } else {
                            logger.debug("数据转发成功: sessionId={}, connectionId={}, bytes={}",
                                    sessionId, connectionId, dataSize);
                        }
                    });
        } else {
            logger.debug("收到空数据包: sessionId={}", sessionId);
        }
    }

    /**
     * 处理关闭包
     */
    private void handleClosePacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        String connectionId = sessionConnections.remove(sessionId);
        sessionHostKeys.remove(sessionId);

        // 回收会话ID以供重用
        if (sessionId > 0 && sessionId <= 10000) {
            reusableSessionIds.offer(sessionId);
            logger.debug("回收会话ID {} 以供重用 (连接ID: {})", sessionId, clientConnectionId);
        }

        // 标记该会话已终止，防止客户端后续重复数据触发日志洪泛
        terminatedSessions.put(sessionId, System.currentTimeMillis());

        if (connectionId != null) {
            logger.debug("关闭会话 {} (连接ID: {})", sessionId, clientConnectionId);

            // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
            proxyProcessor.closeConnection(connectionId)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            logger.warn("关闭连接失败: sessionId={}, connectionId={} (连接ID: {})",
                                    sessionId, connectionId, clientConnectionId, throwable);
                        } else {
                            logger.debug("连接关闭成功: sessionId={}, connectionId={} (连接ID: {})",
                                    sessionId, connectionId, clientConnectionId);
                        }
                        PerformanceMetrics.getInstance().decrementActiveSessions();
                    });
        } else {
            logger.debug("尝试关闭不存在的会话 {} (连接ID: {})", sessionId, clientConnectionId);
        }
    }

    /**
     * 处理心跳包
     * 客户端发送心跳包，服务端简单回复心跳响应
     */
    private void handleHeartbeat(MultiplexProtocol.Packet packet) {
        try {
            logger.debug("收到客户端心跳包，回复心跳响应");

            // 创建心跳响应包
            MultiplexProtocol.Packet response = MultiplexProtocol.createHeartbeatPacket();
            ByteBuf responseBuffer = response.encode();

            // 发送心跳响应
            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (future.isSuccess()) {
                    logger.debug("心跳响应发送成功");
                } else {
                    logger.warn("心跳响应发送失败", future.cause());
                }
            });

        } catch (Exception e) {
            logger.error("处理心跳包时发生异常", e);
        }
    }

    /**
     * 发送连接响应
     */
    private void sendConnectResponse(int sessionId, byte status) {
        try {
            MultiplexProtocol.Packet response = MultiplexProtocol.createConnectResponseV2(sessionId, status);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.error("发送连接响应失败: sessionId={}, status={}", sessionId, status, future.cause());
                } else {
                    logger.debug("发送连接响应成功: sessionId={}, status={}", sessionId, status);
                }
            });
        } catch (Exception e) {
            logger.error("创建连接响应失败: sessionId={}, status={}", sessionId, status, e);
        }
    }

    /**
     * 发送relay连接响应
     */
    private void sendRelayResponse(String requestId, byte status) {
        try {
            MultiplexProtocol.Packet response = MultiplexProtocol.createRelayResponse(requestId, status);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.error("发送relay响应失败: requestId={}, status={}", requestId, status, future.cause());
                } else {
                    logger.debug("发送relay响应成功: requestId={}, status={}", requestId, status);
                }
            });
        } catch (Exception e) {
            logger.error("创建relay响应失败: requestId={}, status={}", requestId, status, e);
        }
    }

    /**
     * 会话连接信息
     */
    private static class SessionConnectionInfo {
        final String connectionId;
        final String hostKey;

        SessionConnectionInfo(String connectionId, String hostKey) {
            this.connectionId = connectionId;
            this.hostKey = hostKey;
        }
    }

    /**
     * 原子性地获取会话连接信息，确保并发安全
     */
    private SessionConnectionInfo getSessionConnectionInfo(int sessionId) {
        synchronized (sessionConnections) {
            String connectionId = sessionConnections.get(sessionId);
            if (connectionId == null) {
                return null;
            }
            String hostKey = sessionHostKeys.get(sessionId);
            return new SessionConnectionInfo(connectionId, hostKey);
        }
    }

    /**
     * 原子性地建立会话映射，确保并发安全
     */
    private boolean establishSessionMapping(int sessionId, String connectionId, String hostKey) {
        synchronized (sessionConnections) {
            // 检查会话ID是否已经被使用
            if (sessionConnections.containsKey(sessionId)) {
                logger.warn("会话ID已存在，无法建立映射: sessionId={}, existingConnectionId={}, newConnectionId={}",
                           sessionId, sessionConnections.get(sessionId), connectionId);
                return false;
            }

            // 检查是否有pending状态的会话
            if (pendingSessions.containsKey(sessionId)) {
                logger.debug("清理pending状态的会话: sessionId={}", sessionId);
                pendingSessions.remove(sessionId);
            }

            // 原子性地建立映射
            sessionConnections.put(sessionId, connectionId);
            sessionHostKeys.put(sessionId, hostKey);

            logger.debug("会话映射建立成功: sessionId={}, connectionId={}, hostKey={}",
                        sessionId, connectionId, hostKey);
            return true;
        }
    }

    /**
     * 处理会话错误
     */
    public void handleSessionError(int sessionId, String reason) {
        // 使用同步块确保会话状态的一致性
        String connectionId;
        synchronized (sessionConnections) {
            // 检查会话是否已经被处理过，避免重复处理
            connectionId = sessionConnections.get(sessionId);
            if (connectionId == null) {
                // 检查是否是pending状态的会话
                if (pendingSessions.containsKey(sessionId)) {
                    logger.debug("清理pending状态的会话: sessionId={}, reason={} (连接ID: {})",
                            sessionId, reason, clientConnectionId);
                    pendingSessions.remove(sessionId);
                } else {
                    logger.debug("会话已被处理，跳过重复错误处理: sessionId={}, reason={} (连接ID: {})",
                            sessionId, reason, clientConnectionId);
                }
                return;
            }

            // 移除会话连接映射
            sessionConnections.remove(sessionId);
            sessionHostKeys.remove(sessionId);
            pendingSessions.remove(sessionId); // 确保也清理pending状态

            // 注销会话路由信息
            MultiplexBackendDataRouter.unregisterSessionRoute(connectionId);
        }

        // 根据错误原因调整日志级别，减少Connection reset错误的噪音
        if (isConnectionResetError(reason)) {
            logger.debug("会话连接被重置: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        } else if (isCommonNetworkError(reason)) {
            logger.debug("会话网络错误: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        } else {
            logger.warn("会话发生错误: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        }

        // 标记该会话已终止，防止客户端后续重复数据触发日志洪泛
        terminatedSessions.put(sessionId, System.currentTimeMillis());

        // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
        proxyProcessor.closeConnection(connectionId);
        PerformanceMetrics.getInstance().decrementActiveSessions();

        // 发送关闭通知给客户端
        sendCloseNotification(sessionId, reason);
    }

    /**
     * 判断是否为连接重置错误
     */
    private boolean isConnectionResetError(String reason) {
        return reason != null && (reason.contains("Connection reset by peer") ||
                reason.contains("Connection reset") ||
                reason.contains("连接被重置") ||
                reason.contains("远程主机强迫关闭了一个现有的连接"));
    }

    /**
     * 判断是否为常见网络错误
     */
    private boolean isCommonNetworkError(String reason) {
        return reason != null && (reason.contains("timeout") ||
                reason.contains("超时") ||
                reason.contains("Connection refused") ||
                reason.contains("连接被拒绝") ||
                reason.contains("Network is unreachable") ||
                reason.contains("网络不可达"));
    }

    /**
     * 发送关闭通知给客户端
     */
    private void sendCloseNotification(int sessionId, String reason) {
        try {
            MultiplexProtocol.Packet closePacket = MultiplexProtocol.createClosePacket(sessionId);
            ByteBuf closeBuffer = closePacket.encode();
            clientChannel.writeAndFlush(closeBuffer);

            logger.debug("发送关闭通知: sessionId={}, reason={}", sessionId, reason);
        } catch (Exception e) {
            logger.warn("发送关闭通知失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 处理连接失败
     */
    private void handleConnectionFailure(int sessionId, String host, int port, String protocol, Throwable cause) {
        byte statusCode = MultiplexProtocol.STATUS_FAILED;
        if (cause instanceof java.net.ConnectException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("目标主机不可达: {}:{}", host, port);
        } else if (cause instanceof java.net.UnknownHostException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("无法解析主机名: {}", host);
        } else if (cause instanceof java.util.concurrent.TimeoutException ||
                (cause.getMessage() != null && cause.getMessage().contains("timeout"))) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("连接超时: {}:{}", host, port);
        }

        sendConnectResponse(sessionId, statusCode);
    }

    /**
     * 格式化地址用于显示
     */
    private String formatAddressForDisplay(String host, int port) {
        if (host.contains(":") && !host.startsWith("[")) {
            return "[" + host + "]:" + port;
        }
        return host + ":" + port;
    }

    /**
     * 优化缓冲区管理
     */
    private void optimizeBuffer() {
        if (buffer == null)
            return;

        int readableBytes = buffer.readableBytes();
        if (readableBytes == 0) {
            // 缓冲区为空，清理
            buffer.clear();
        } else if (readableBytes < buffer.capacity() / 4 && buffer.capacity() > 1024) {
            // 缓冲区使用率低且较大，压缩以节省内存
            buffer.discardReadBytes();
            if (buffer.capacity() > readableBytes * 4) {
                // 创建更小的缓冲区
                ByteBuf newBuffer = buffer.alloc().buffer(Math.max(readableBytes * 2, 256));
                newBuffer.writeBytes(buffer);
                buffer.release();
                buffer = newBuffer;
            }
        } else {
            // 正常压缩
            buffer.discardReadBytes();
        }
    }

    /**
     * 根据会话ID获取连接
     */
    private OutboundConnection getConnectionBySessionId(int sessionId) {
        String connectionId = sessionConnections.get(sessionId);
        if (connectionId == null) {
            return null;
        }
        return proxyProcessor.getActiveConnection(connectionId);
    }

    /**
     * 检查会话是否存在
     */
    private boolean isSessionActive(int sessionId) {
        String connectionId = sessionConnections.get(sessionId);
        if (connectionId == null) {
            return false;
        }

        OutboundConnection connection = proxyProcessor.getActiveConnection(connectionId);
        return connection != null && connection.isActive();
    }

    /**
     * 安全地分配会话ID，支持ID重用
     */
    private int allocateSessionId() {
        // 首先尝试重用已释放的会话ID
        Integer reusedId = reusableSessionIds.poll();
        if (reusedId != null && !sessionConnections.containsKey(reusedId)) {
            logger.debug("重用会话ID: {}", reusedId);
            return reusedId;
        }

        // 如果没有可重用的ID，生成新的ID
        int maxAttempts = 100;
        int attempts = 0;

        while (attempts < maxAttempts) {
            int sessionId = sessionIdGenerator.getAndIncrement();

            // 避免sessionId溢出，重置为1
            if (sessionId <= 0 || sessionId > 10000) {
                sessionIdGenerator.set(1);
                sessionId = sessionIdGenerator.getAndIncrement();
            }

            // 检查是否已存在
            if (!sessionConnections.containsKey(sessionId)) {
                return sessionId;
            }

            attempts++;
        }

        // 如果无法分配，返回-1表示失败
        logger.error("无法分配会话ID，尝试了{}次", maxAttempts);
        return -1;
    }

    /**
     * 获取会话统计信息
     */
    public int getActiveSessionCount() {
        return sessionConnections.size();
    }

    /**
     * 获取详细的会话统计信息
     */
    public String getSessionStats() {
        int activeCount = sessionConnections.size();
        int pendingCount = pendingSessions.size();
        int terminatedCount = terminatedSessions.size();
        int reusableCount = reusableSessionIds.size();

        return String.format("MultiplexSession[%d] - 活跃: %d, 待建立: %d, 已终止: %d, 可重用ID: %d",
                clientConnectionId, activeCount, pendingCount, terminatedCount, reusableCount);
    }

    /**
     * 检查并清理不健康的连接
     */
    public void checkAndCleanupUnhealthyConnections() {
        int cleanedCount = 0;

        for (Map.Entry<Integer, String> entry : sessionConnections.entrySet()) {
            int sessionId = entry.getKey();
            String connectionId = entry.getValue();

            OutboundConnection connection = proxyProcessor.getActiveConnection(connectionId);
            if (connection == null || !connection.isActive()) {
                logger.debug("清理不健康连接: sessionId={}, connectionId={}", sessionId, connectionId);
                handleSessionError(sessionId, "连接不健康");
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            logger.info("清理了 {} 个不健康的会话连接", cleanedCount);
        }
    }

    /**
     * 清理会话
     */
    public void cleanup() {
        logger.debug("开始清理多路复用会话，活跃会话数: {} (连接ID: {})",
                sessionConnections.size(), clientConnectionId);

        // 安全地关闭所有会话
        int closedSessions = 0;
        for (Map.Entry<Integer, String> entry : sessionConnections.entrySet()) {
            int sessionId = entry.getKey();
            String connectionId = entry.getValue();

            try {
                // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
                proxyProcessor.closeConnection(connectionId);
                closedSessions++;

                // 性能指标统计
                PerformanceMetrics.getInstance().decrementActiveSessions();
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: sessionId={}, connectionId={} (连接ID: {}): {}",
                        sessionId, connectionId, clientConnectionId, e.getMessage());
            }
        }

        // 清理所有映射
        sessionConnections.clear();
        sessionHostKeys.clear();
        reusableSessionIds.clear();
        terminatedSessions.clear();

        if (closedSessions > 0) {
            logger.info("客户端连接 {} 断开，已清理 {} 个后端会话", clientConnectionId, closedSessions);
        }

        // 安全释放缓冲区
        if (buffer != null) {
            try {
                buffer.release();
                logger.debug("已释放客户端连接 {} 的缓冲区", clientConnectionId);
            } catch (Exception e) {
                logger.warn("释放缓冲区时出现异常 (连接ID: {}): {}", clientConnectionId, e.getMessage());
            } finally {
                buffer = null;
            }
        }

        logger.debug("多路复用会话清理完成 (连接ID: {})", clientConnectionId);
    }

    /**
     * 判断是否为连接重置错误
     */
    private boolean isConnectionResetError(Throwable cause) {
        if (cause instanceof java.io.IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Connection reset by peer") ||
                    message.contains("Connection reset") ||
                    message.contains("远程主机强迫关闭了一个现有的连接"));
        }
        return false;
    }

    /**
     * 检查会话是否正在建立连接中
     */
    private boolean isPendingSession(int sessionId) {
        Long pendingTime = pendingSessions.get(sessionId);
        if (pendingTime == null) {
            return false;
        }

        // 检查是否超时（超过30秒认为连接建立失败）
        long now = System.currentTimeMillis();
        if (now - pendingTime > 30_000L) {
            // 超时，移除pending状态
            pendingSessions.remove(sessionId);
            logger.warn("会话连接建立超时，移除pending状态: sessionId={} (连接ID: {})",
                    sessionId, clientConnectionId);
            return false;
        }

        return true;
    }

    /**
     * 判断是否为通道已关闭错误
     */
    private boolean isClosedChannelError(Throwable cause) {
        // 检查StacklessClosedChannelException和其他通道关闭异常
        if (cause.getClass().getSimpleName().equals("StacklessClosedChannelException")) {
            return true;
        }
        if (cause instanceof java.nio.channels.ClosedChannelException) {
            return true;
        }
        if (cause instanceof java.io.IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Channel is closed") ||
                    message.contains("通道已关闭") ||
                    message.contains("Connection is closed"));
        }
        return false;
    }
}
